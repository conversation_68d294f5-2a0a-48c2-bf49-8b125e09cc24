# Project Tag Filter Implementation Test Plan

## Overview
This document outlines the test cases and procedures to verify the project tag filtering functionality works correctly.

## Prerequisites
1. Running API server
2. Valid authentication token
3. Test data with projects that have:
   - Different project tags
   - Projects with multiple tags
   - Projects with no tags
   - Projects with overlapping tags

## Test Cases

### 1. Basic Tag Filtering Tests

#### Test 1.1: Single Tag Filter
```bash
curl -X GET "http://localhost:3001/projects?tags=production&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only projects that have a tag named "production"

#### Test 1.2: Multiple Tags Filter (AND operation)
```bash
curl -X GET "http://localhost:3001/projects?tags=production,critical&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only projects that have BOTH "production" AND "critical" tags

#### Test 1.3: Tag Filter with Spaces
```bash
curl -X GET "http://localhost:3001/projects?tags=high%20priority,production&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only projects that have BOTH "high priority" AND "production" tags

#### Test 1.4: Non-existent Tag
```bash
curl -X GET "http://localhost:3001/projects?tags=nonexistent&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Empty result set (no projects)

#### Test 1.5: Empty Tags Parameter
```bash
curl -X GET "http://localhost:3001/projects?tags=&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** All projects (no filtering applied)

### 2. Combined Filtering Tests

#### Test 2.1: Tag Filter with Status Filter
```bash
curl -X GET "http://localhost:3001/projects?tags=production&status=ACTIVE&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only ACTIVE projects that have "production" tag

#### Test 2.2: Tag Filter with Provider Type Filter
```bash
curl -X GET "http://localhost:3001/projects?tags=aws,production&provider_type=AWS&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only AWS projects that have BOTH "aws" AND "production" tags

#### Test 2.3: Tag Filter with Organization Filter
```bash
curl -X GET "http://localhost:3001/projects?tags=critical&organization_id=ORG_ID&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only projects in specified organization that have "critical" tag

#### Test 2.4: Tag Filter with Search Query
```bash
curl -X GET "http://localhost:3001/projects?tags=production&q=test&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only projects with "production" tag that match search term "test"

### 3. Sorting with Tag Filtering Tests

#### Test 3.1: Tag Filter with Code Sorting
```bash
curl -X GET "http://localhost:3001/projects?tags=production&sort_by=code_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects with "production" tag, sorted by code descending

#### Test 3.2: Tag Filter with Budget Sorting
```bash
curl -X GET "http://localhost:3001/projects?tags=high-budget&sort_by=budget_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects with "high-budget" tag, sorted by budget descending

### 4. Edge Cases Tests

#### Test 4.1: Tags with Special Characters
```bash
curl -X GET "http://localhost:3001/projects?tags=test%2Denv,prod%2Dready&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects with "test-env" AND "prod-ready" tags

#### Test 4.2: Case Sensitivity Test
```bash
curl -X GET "http://localhost:3001/projects?tags=Production&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only projects with exactly "Production" tag (case-sensitive)

#### Test 4.3: Whitespace Handling
```bash
curl -X GET "http://localhost:3001/projects?tags=%20production%20,%20critical%20&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects with "production" AND "critical" tags (whitespace trimmed)

### 5. Pagination with Tag Filtering Tests

#### Test 5.1: Multiple Pages with Tag Filter
```bash
# Page 1
curl -X GET "http://localhost:3001/projects?tags=production&page=1&limit=3" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Page 2
curl -X GET "http://localhost:3001/projects?tags=production&page=2&limit=3" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Consistent filtering across pages, no duplicate or missing projects

### 6. Performance Tests

#### Test 6.1: Large Number of Tags
```bash
curl -X GET "http://localhost:3001/projects?tags=tag1,tag2,tag3,tag4,tag5&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects that have ALL specified tags (performance should be reasonable)

## Automated Test Script

```bash
#!/bin/bash

# Configuration
BASE_URL="http://localhost:3001"
TOKEN="YOUR_AUTH_TOKEN_HERE"

echo "Starting Project Tag Filter Tests..."

# Test 1: Single tag filter
echo "Test 1: Single tag filter"
curl -s -X GET "$BASE_URL/projects?tags=production&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[] | {code: .code, tags: .project_tags[].name}'

# Test 2: Multiple tags filter
echo "Test 2: Multiple tags filter"
curl -s -X GET "$BASE_URL/projects?tags=production,critical&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[] | {code: .code, tags: .project_tags[].name}'

# Test 3: Non-existent tag
echo "Test 3: Non-existent tag"
curl -s -X GET "$BASE_URL/projects?tags=nonexistent&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data | length'

# Test 4: Combined with status filter
echo "Test 4: Combined with status filter"
curl -s -X GET "$BASE_URL/projects?tags=production&status=ACTIVE&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[] | {code: .code, status: .status, tags: .project_tags[].name}'

# Test 5: Empty tags parameter
echo "Test 5: Empty tags parameter"
curl -s -X GET "$BASE_URL/projects?tags=&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data | length'

echo "Tests completed!"
```

## Manual Verification Steps

1. **Verify Single Tag Filtering:**
   - Check that only projects with the specified tag are returned
   - Verify that the project_tags array contains the filtered tag

2. **Verify Multiple Tag Filtering (AND operation):**
   - Check that only projects having ALL specified tags are returned
   - Verify that each returned project has all the specified tags

3. **Verify Combined Filtering:**
   - Check that tag filters work correctly with other filters
   - Verify that all filter conditions are applied simultaneously

4. **Verify Edge Cases:**
   - Confirm that whitespace is properly trimmed from tag names
   - Verify that empty tag parameters don't break the filtering
   - Check that non-existent tags return empty results

5. **Verify Performance:**
   - Test with multiple tags to ensure reasonable response times
   - Verify that the SQL query is optimized (check logs if available)

## Expected SQL Query

The tag filtering should generate a SQL query similar to:
```sql
SELECT * FROM projects 
WHERE id IN (
  SELECT project_id 
  FROM project_tags 
  WHERE name IN ('tag1', 'tag2', 'tag3') 
  GROUP BY project_id 
  HAVING COUNT(DISTINCT name) = 3
)
```

This ensures that only projects having ALL specified tags are returned.

## Expected Results Summary

- **Single tag filtering:** Should return only projects with that specific tag
- **Multiple tag filtering:** Should return only projects with ALL specified tags (AND operation)
- **Combined filtering:** Should work seamlessly with existing filters
- **Edge cases:** Should handle whitespace, empty parameters, and special characters properly
- **Performance:** Should be reasonably fast even with multiple tags
- **Pagination:** Should maintain consistent filtering across pages
