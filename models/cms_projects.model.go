package models

type CMSProjectType string

const (
	CMSProjectTypeNew       CMSProjectType = "NEW"
	CMSProjectTypeMigration CMSProjectType = "MIGRATION"
)

type CMSProjectStatus string

const (
	CMSProjectStatusDraft     CMSProjectStatus = "DRAFT"
	CMSProjectStatusNew       CMSProjectStatus = "NEW"
	CMSProjectStatusInProcess CMSProjectStatus = "IN_PROCESS"
	CMSProjectStatusActive    CMSProjectStatus = "ACTIVE"
	CMSProjectStatusClose     CMSProjectStatus = "CLOSE"
)

type CMSProject struct {
	BaseModel
	MinistryID   string           `json:"ministry_id" gorm:"column:ministry_id"`
	DepartmentID string           `json:"department_id" gorm:"column:department_id"`
	DivisionID   string           `json:"division_id" gorm:"column:division_id"`
	PhaseID      *string          `json:"phase_id" gorm:"column:phase_id"`
	Name         string           `json:"name" gorm:"column:name"`
	Type         CMSProjectType   `json:"type" gorm:"column:type;default:NEW"`
	Domain       string           `json:"domain" gorm:"column:domain"`
	ContactName  string           `json:"contact_name" gorm:"column:contact_name"`
	ContactPhone string           `json:"contact_phone" gorm:"column:contact_phone"`
	ContactEmail string           `json:"contact_email" gorm:"column:contact_email"`
	Remark       string           `json:"remark" gorm:"column:remark"`
	Status       CMSProjectStatus `json:"status" gorm:"column:status;default:DRAFT"`
	CreatedByID  *string          `json:"created_by_id" gorm:"column:created_by_id"`
	UpdatedByID  *string          `json:"updated_by_id" gorm:"column:updated_by_id"`
	DeletedByID  *string          `json:"deleted_by_id" gorm:"column:deleted_by_id"`
	CreatedBy    *User            `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	UpdatedBy    *User            `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID"`
	DeletedBy    *User            `json:"deleted_by,omitempty" gorm:"foreignKey:DeletedByID"`

	Phases     []CMSProjectPhase `json:"phases,omitempty" gorm:"foreignKey:CMSProjectID"`
	Phase      *CMSProjectPhase  `json:"phase,omitempty" gorm:"foreignKey:PhaseID"`
	Ministry   *Ministry         `json:"ministry,omitempty" gorm:"foreignKey:MinistryID"`
	Department *Department       `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	Division   *Division         `json:"division,omitempty" gorm:"foreignKey:DivisionID"`
}

func (CMSProject) TableName() string {
	return "cms_projects"
}
