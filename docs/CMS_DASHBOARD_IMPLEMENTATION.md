# CMS Dashboard Implementation

This document describes the implementation of the CMS Dashboard functionality for the CMS module.

## Overview

The CMS Dashboard provides comprehensive statistics and insights about CMS projects, including:
- Project statistics by status
- Latest projects
- Projects grouped by status with percentages
- Nearly expired projects (within 3 months)

## Implementation Details

### 1. Service Layer (`services/cms_project.service.go`)

#### Interface Extension
Added `GetDashboardData() (*CMSDashboardResponse, core.IError)` to the `ICMSProjectService` interface.

#### Main Dashboard Method
- `GetDashboardData()`: Orchestrates parallel data collection using goroutines and channels
- Uses sync.WaitGroup for concurrent execution of 4 data collection operations
- Returns comprehensive dashboard data in a structured format

#### Helper Methods
- `getCMSProjectStats()`: Retrieves project counts by status (Draft, New, In Process, Active, Close)
- `getLatestCMSProjects()`: Gets the 5 most recently created projects with full relations
- `getCMSProjectsByStatus()`: Calculates project distribution by status with percentages
- `getNearlyExpiredCMSProjects()`: Finds active/in-process projects expiring within 3 months

### 2. Data Transfer Objects (`services/cms_project.dto.go`)

#### Response Structures
- `CMSDashboardResponse`: Main dashboard response containing all dashboard data
- `CMSProjectStats`: Project count statistics by status
- `CMSStatusStat`: Status distribution with count and percentage

#### Key Features
- Comprehensive project statistics
- Percentage calculations for status distribution
- Full project relations included in latest and nearly expired projects

### 3. Controller Layer (`modules/cms/cms_project.controller.go`)

#### Dashboard Endpoint
- `Dashboard(c core.IHTTPContext) error`: HTTP handler for the dashboard endpoint
- Returns JSON response with dashboard data
- Handles errors appropriately with proper HTTP status codes

### 4. HTTP Routes (`modules/cms/cms.http.go`)

The dashboard route is already defined:
```go
e.GET("/cms/dashboard", core.WithHTTPContext(cmsProject.Dashboard), middleware.AuthMiddleware())
```

## API Endpoint

### GET /cms/dashboard

**Authentication**: Required (Bearer token)

**Response Structure**:
```json
{
  "project_stats": {
    "total": 100,
    "draft": 20,
    "new": 15,
    "in_process": 30,
    "active": 25,
    "close": 10
  },
  "latest_projects": [
    {
      "id": "uuid",
      "name": "Project Name",
      "status": "ACTIVE",
      "created_at": "2024-01-01T00:00:00Z",
      "ministry": {...},
      "department": {...},
      "division": {...}
    }
  ],
  "projects_by_status": [
    {
      "status": "DRAFT",
      "count": 20,
      "percent": 20.0
    },
    {
      "status": "NEW",
      "count": 15,
      "percent": 15.0
    }
  ],
  "nearly_expired_projects": [
    {
      "id": "uuid",
      "name": "Expiring Project",
      "end_date": "2024-04-01",
      "status": "ACTIVE"
    }
  ]
}
```

## Performance Optimizations

1. **Parallel Processing**: Uses goroutines to fetch different data sets concurrently
2. **Efficient Queries**: Optimized database queries with appropriate WHERE clauses
3. **Selective Preloading**: Only loads necessary relations for dashboard display
4. **Limited Results**: Latest projects limited to 5 items for performance

## Business Logic

### Nearly Expired Projects
- Considers projects with end_date within 3 months from current date
- Only includes ACTIVE and IN_PROCESS projects
- Ordered by end_date (earliest first)

### Status Distribution
- Calculates percentages based on total project count
- Handles division by zero gracefully
- Includes all CMS project statuses: DRAFT, NEW, IN_PROCESS, ACTIVE, CLOSE

## Testing

Use the provided `test_cms_dashboard.http` file to test the dashboard endpoint:

```http
GET http://localhost:8080/cms/dashboard
Authorization: Bearer your_auth_token_here
```

## Error Handling

- Proper error responses with appropriate HTTP status codes
- Detailed error messages for debugging
- Graceful handling of database connection issues
- Concurrent error collection and reporting

## Dependencies

- Existing CMS project repository methods
- Core framework error handling
- Authentication middleware
- Database connection and GORM ORM

## Future Enhancements

Potential improvements could include:
- Caching for frequently accessed dashboard data
- Additional filtering options (date ranges, departments, etc.)
- Real-time updates using WebSockets
- Export functionality for dashboard data
- Customizable dashboard widgets
