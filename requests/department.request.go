package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type DepartmentCreate struct {
	core.BaseValidator
	MinistryID *string `json:"ministry_id"`
	NameTh     *string `json:"name_th"`
	NameEn     *string `json:"name_en"`
}

func (r *DepartmentCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.MinistryID, "ministry_id"))
	r.Must(r.Is<PERSON>tr<PERSON>equired(r.NameTh, "name_th"))
	r.Must(r.IsStrRequired(r.NameEn, "name_en"))

	r.Must(r.IsStrUnique(ctx, r.NameTh, models.Department{}.TableName(), "name_th", "", "name_th"))
	r.Must(r.<PERSON>(ctx, r.NameEn, models.Department{}.TableName(), "name_en", "", "name_en"))
	// Check if ministry exists
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	return r.Error()
}

type DepartmentUpdate struct {
	core.BaseValidator
	MinistryID *string `json:"ministry_id"`
	NameTh     *string `json:"name_th"`
	NameEn     *string `json:"name_en"`
}

func (r *DepartmentUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldNameTh := ""
	oldNameEn := ""
	department, _ := repo.Department(cc).FindOne("id = ?", cc.Param("id"))
	if department != nil {
		oldNameTh = department.NameTh
		if department.NameEn != nil {
			oldNameEn = utils.ToNonPointer(department.NameEn)
		}
	}

	if utils.ToNonPointer(r.NameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameTh, models.Department{}.TableName(), "name_th", oldNameTh, "name_th"))
	}

	if utils.ToNonPointer(r.NameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Department{}.TableName(), "name_en", oldNameEn, "name_en"))
	}

	// Check if ministry exists if provided
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	return r.Error()
}
