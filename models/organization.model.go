package models

type Organization struct {
	BaseModelHardDelete
	NameTh      string  `json:"name_th" gorm:"column:name_th"`
	NameEn      *string `json:"name_en" gorm:"column:name_en"`
	ShortNameTh *string `json:"short_name_th" gorm:"column:short_name_th"`
	ShortNameEn *string `json:"short_name_en" gorm:"column:short_name_en"`
	Code        *int    `json:"code" gorm:"column:code"`

	// Projects []Project `json:"projects,omitempty" gorm:"foreignKey:OrganizationID"`
}

func (Organization) TableName() string {
	return "organizations"
}
