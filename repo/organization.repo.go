package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type OrganizationOption func(repository.IRepository[models.Organization])

var Organization = func(c core.IContext, options ...OrganizationOption) repository.IRepository[models.Organization] {
	r := repository.New[models.Organization](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func OrganizationOrderBy(pageOptions *core.PageOptions) OrganizationOption {
	return func(c repository.IRepository[models.Organization]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func OrganizationWithProjects() OrganizationOption {
	return func(c repository.IRepository[models.Organization]) {
		c.Preload("Projects")
	}
}

func OrganizationWithSearch(search string) OrganizationOption {
	if search == "" {
		return func(c repository.IRepository[models.Organization]) {}
	}
	return func(c repository.IRepository[models.Organization]) {
		searchPattern := "%" + search + "%"
		c.Where("name_th ILIKE ? OR name_en ILIKE ? OR short_name_th ILIKE ? OR short_name_en ILIKE ? ", searchPattern, searchPattern, searchPattern, searchPattern)
	}
}
