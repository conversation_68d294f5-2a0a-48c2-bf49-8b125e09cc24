import { PrismaClient } from "@prisma/client";
import * as bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function user() {
  // Hash the password using bcrypt (same as <PERSON>'s bcrypt.DefaultCost which is 10)
  const hashedPassword = await bcrypt.hash("12345678", 10);

  // Check if admin user already exists
  const existingAdmin = await prisma.users.findUnique({
    where: { email: "<EMAIL>" }
  });

  if (existingAdmin) {
    console.log("Admin user already exists, skipping creation");
    return;
  }

  // Create admin user
  const adminUser = await prisma.users.create({
    data: {
      email: "<EMAIL>",
      password: hashedPassword,
      display_name: "admin",
      type: "ADMIN",
    }
  });

  console.log("Admin user created:", {
    id: adminUser.id,
    email: adminUser.email,
    display_name: adminUser.display_name
  });
}

// [
//   {
//     "ลำดับ": 1,
//     "งวดที่": 2,
//     "กระทรวง": "กระทรวงศึกษาธิการ",
//     "กรม": "สำนักงานคณะกรรมการการศึกษาขั้นพื้นฐาน",
//     "หน่วยงาน": "สำนักงานเขตพื้นที่การศึกษาประถมศึกษากำแพงเพชร เขต 2",
//     "รูปแบบการใช้บริการ": "New CMS",
//     "Domain Name ที่ขอใช้บริการ": "kpp2.go.th",
//     "Domain Name ที่ให้บริการ *": "* kpp2.cms.opendata.go.th",
//     "Domain Name ที่เปลี่ยนแปลง": "",
//     "วันที่ส่งมอบ User Account": "17 ก.พ. 2568",
//     "วันที่เริ่มต้นใช้งาน": "19 ก.พ. 2568",
//     "วันที่สิ้นสุดการใช้งาน (1ปี)": "18 ก.พ. 2569",
//     "หมายเหตุ": "",
//     "opendata": 1,
//     "หน่วยงาน__1": "",
//     "cloudfare ของหน่วยงานเอง": ""
//   },
//   {
//     "ลำดับ": 2,
//     "งวดที่": 2,
//     "กระทรวง": "กระทรวงสาธารณสุข",
//     "กรม": "สำนักงานปลัดกระทรวงสาธารณสุข",
//     "หน่วยงาน": "โรงพยาบาลหนองหิน",
//     "รูปแบบการใช้บริการ": "New CMS",
//     "Domain Name ที่ขอใช้บริการ": "nonghinhospital.moph.go.th",
//     "Domain Name ที่ให้บริการ *": "* nonghinhospital.cms.opendata.go.th",
//     "Domain Name ที่เปลี่ยนแปลง": "",
//     "วันที่ส่งมอบ User Account": "17 ก.พ. 2568",
//     "วันที่เริ่มต้นใช้งาน": "19 ก.พ. 2568",
//     "วันที่สิ้นสุดการใช้งาน (1ปี)": "18 ก.พ. 2569",
//     "หมายเหตุ": "",
//     "opendata": 1,
//     "หน่วยงาน__1": "",
//     "cloudfare ของหน่วยงานเอง": ""
//   },
//   {
//     "ลำดับ": 3,
//     "งวดที่": 2,
//     "กระทรวง": "กระทรวงเกษตรและสหกรณ์",
//     "กรม": "สำนักงานสภาเกษตรกรแห่งชาติ",
//     "หน่วยงาน": "สำนักงานสภาเกษตรกรแห่งชาติ",
//     "รูปแบบการใช้บริการ": "New CMS",
//     "Domain Name ที่ขอใช้บริการ": "nfc.or.th",
//     "Domain Name ที่ให้บริการ *": "* nfc.cms.opendata.go.th",
//     "Domain Name ที่เปลี่ยนแปลง": "",
//     "วันที่ส่งมอบ User Account": "17 ก.พ. 2568",
//     "วันที่เริ่มต้นใช้งาน": "19 ก.พ. 2568",
//     "วันที่สิ้นสุดการใช้งาน (1ปี)": "18 ก.พ. 2569",
//     "หมายเหตุ": "",
//     "opendata": 1,
//     "หน่วยงาน__1": "",
//     "cloudfare ของหน่วยงานเอง": ""
//   },
// ]
async function cmsProject() {
  console.log("Starting CMS Project seeding...");

  // Sample CMS projects based on the commented data
  const cmsProjectsData = [
    {
      name: "สำนักงานเขตพื้นที่การศึกษาประถมศึกษากำแพงเพชร เขต 2",
      type: "NEW" as const,
      domain: "kpp2.cms.opendata.go.th",
      contact_name: "ผู้ดูแลระบบ กพ.2",
      contact_phone: "055-123-456",
      contact_email: "<EMAIL>",
      remark: "งวดที่ 2 - กระทรวงศึกษาธิการ",
      status: "ACTIVE" as const,
    },
    {
      name: "โรงพยาบาลหนองหิน",
      type: "NEW" as const,
      domain: "nonghinhospital.cms.opendata.go.th",
      contact_name: "ผู้ดูแลระบบ รพ.หนองหิน",
      contact_phone: "043-123-456",
      contact_email: "<EMAIL>",
      remark: "งวดที่ 2 - กระทรวงสาธารณสุข",
      status: "ACTIVE" as const,
    },
    {
      name: "สำนักงานสภาเกษตรกรแห่งชาติ",
      type: "NEW" as const,
      domain: "nfc.cms.opendata.go.th",
      contact_name: "ผู้ดูแลระบบ สภาเกษตรกร",
      contact_phone: "02-123-456",
      contact_email: "<EMAIL>",
      remark: "งวดที่ 2 - กระทรวงเกษตรและสหกรณ์",
      status: "ACTIVE" as const,
    },
    {
      name: "โครงการ CMS ทดสอบ 1",
      type: "NEW" as const,
      domain: "test1.cms.opendata.go.th",
      contact_name: "ผู้ทดสอบ 1",
      contact_phone: "02-111-1111",
      contact_email: "<EMAIL>",
      remark: "โครงการทดสอบระบบ CMS",
      status: "DRAFT" as const,
    },
    {
      name: "โครงการ Migration CMS",
      type: "MIGRATION" as const,
      domain: "migration.cms.opendata.go.th",
      contact_name: "ผู้ดูแล Migration",
      contact_phone: "02-222-2222",
      contact_email: "<EMAIL>",
      remark: "โครงการย้ายข้อมูลจากระบบเก่า",
      status: "IN_PROCESS" as const,
    }
  ];

  for (const projectData of cmsProjectsData) {
    // Check if project already exists
    const existingProject = await prisma.cms_projects.findUnique({
      where: { name: projectData.name }
    });

    if (existingProject) {
      console.log(`CMS Project "${projectData.name}" already exists, skipping creation`);
      continue;
    }

    // Create CMS project
    const cmsProject = await prisma.cms_projects.create({
      data: projectData
    });

    console.log(`CMS Project created: ${cmsProject.name} (${cmsProject.domain})`);

    // Create sample phases for some projects
    if (projectData.status === "ACTIVE") {
      const phases = [
        {
          cms_project_id: cmsProject.id,
          phase: 1,
          work_phase: 1,
          start_date: new Date("2024-02-19"),
          end_date: new Date("2024-05-19"),
          type: projectData.type,
          file_url: `https://example.com/files/${cmsProject.id}/phase1.pdf`
        },
        {
          cms_project_id: cmsProject.id,
          phase: 2,
          work_phase: 1,
          start_date: new Date("2024-05-20"),
          end_date: new Date("2024-08-19"),
          type: projectData.type,
          file_url: `https://example.com/files/${cmsProject.id}/phase2.pdf`
        }
      ];

      for (const phaseData of phases) {
        const phase = await prisma.cms_project_phases.create({
          data: phaseData
        });
        console.log(`  - Phase ${phase.phase} created for project ${cmsProject.name}`);
      }
    }
  }

  console.log("CMS Project seeding completed!");
}
user()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
