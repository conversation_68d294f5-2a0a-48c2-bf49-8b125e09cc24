package services

// import (
// 	"fmt"
// 	"testing"

// 	"github.com/stretchr/testify/assert"
// )

// func TestGetBillingFromHuaweiCloud(t *testing.T) {
// 	t.Run("Success", func(t *testing.T) {
// 		_, err := getBillingFromHuaweiCloud("2025-08-01", "2025-08-10")

// 		// Assertions
// 		assert.NoError(t, err)
// 	})
// }

// func TestGetTokenFromAKSK(t *testing.T) {
// 	t.Run("Success", func(t *testing.T) {
// 		token, err := getTokenFromAKSK()

// 		fmt.Println(token, "token")

// 		// Assertions
// 		assert.NoError(t, err)
// 		assert.NotEmpty(t, token)
// 	})

// 	t.Run("Request Creation Failure", func(t *testing.T) {
// 		// Invalid URL to cause http.NewRequest to fail

// 		token, err := getTokenFromAKSK()

// 		assert.Error(t, err)
// 		assert.Empty(t, token)
// 	})
// }
