package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ProjectItemOption func(repository.IRepository[models.ProjectItem])

var ProjectItem = func(c core.IContext, options ...ProjectItemOption) repository.IRepository[models.ProjectItem] {
	r := repository.New[models.ProjectItem](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func ProjectItemOrderBy(pageOptions *core.PageOptions) ProjectItemOption {
	return func(c repository.IRepository[models.ProjectItem]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}
