package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type OrganizationCreate struct {
	core.BaseValidator
	NameTh      *string `json:"name_th"`
	NameEn      *string `json:"name_en"`
	ShortNameTh *string `json:"short_name_th"`
	ShortNameEn *string `json:"short_name_en"`
}

func (r *OrganizationCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.Is<PERSON>trRequired(r.NameTh, "name_th"))
	r.Must(r.Is<PERSON>trRequired(r.NameEn, "name_en"))

	r.Must(r.IsStrUnique(ctx, r.NameTh, models.Organization{}.TableName(), "name_th", "", "name_th"))
	r.Must(r.<PERSON>(ctx, r.Name<PERSON>n, models.Organization{}.TableName(), "name_en", "", "name_en"))
	r.Must(r.IsStrUnique(ctx, r.ShortNameTh, models.Organization{}.TableName(), "short_name_th", "", "short_name_th"))
	r.Must(r.IsStrUnique(ctx, r.ShortNameEn, models.Organization{}.TableName(), "short_name_en", "", "short_name_en"))

	return r.Error()
}

type OrganizationUpdate struct {
	core.BaseValidator
	NameTh      *string `json:"name_th"`
	NameEn      *string `json:"name_en"`
	ShortNameTh *string `json:"short_name_th"`
	ShortNameEn *string `json:"short_name_en"`
}

func (r *OrganizationUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldNameTh := ""
	oldNameEn := ""
	oldShortNameTh := ""
	oldShortNameEn := ""
	organization, _ := repo.Organization(cc).FindOne("id = ?", cc.Param("id"))
	if organization != nil {
		oldNameTh = organization.NameTh
		if organization.NameEn != nil {
			oldNameEn = utils.ToNonPointer(organization.NameEn)
		}
		if organization.ShortNameTh != nil {
			oldShortNameTh = utils.ToNonPointer(organization.ShortNameTh)
		}
		if organization.ShortNameEn != nil {
			oldShortNameEn = utils.ToNonPointer(organization.ShortNameEn)
		}
	}

	if utils.ToNonPointer(r.NameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameTh, models.Organization{}.TableName(), "name_th", oldNameTh, "name_th"))
	}

	if utils.ToNonPointer(r.NameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Organization{}.TableName(), "name_en", oldNameEn, "name_en"))
	}

	if utils.ToNonPointer(r.ShortNameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.ShortNameTh, models.Organization{}.TableName(), "short_name_th", oldShortNameTh, "short_name_th"))
	}

	if utils.ToNonPointer(r.ShortNameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.ShortNameEn, models.Organization{}.TableName(), "short_name_en", oldShortNameEn, "short_name_en"))
	}

	return r.Error()
}
