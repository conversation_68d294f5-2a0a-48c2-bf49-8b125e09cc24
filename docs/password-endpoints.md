# Password Management & Project Search Endpoints

This document describes the newly implemented password management endpoints and enhanced project search/filtering functionality for the CSP API.

## Endpoints

### 1. Change Password (Self-Service)

**Endpoint:** `POST /me/change-password`

**Description:** Allows authenticated users to change their own password by providing their current password and a new password.

**Authentication:** Required (Bearer token)

**Request Body:**
```json
{
  "current_password": "string",
  "new_password": "string"
}
```

**Response:**
- **200 OK:** Password changed successfully
  ```json
  {
    "message": "Password changed successfully"
  }
  ```

- **400 Bad Request:** Invalid current password
  ```json
  {
    "code": "INVALID_CURRENT_PASSWORD",
    "message": "Current password is incorrect"
  }
  ```

- **401 Unauthorized:** Invalid or missing authentication token

**Validation:**
- `current_password`: Required
- `new_password`: Required

### 2. Reset User Password (Admin)

**Endpoint:** `POST /users/:id/reset-password`

**Description:** Allows administrators to reset any user's password without requiring the current password. This also sets the `is_required_reset_password` flag to `false`.

**Authentication:** Required (Bearer token)

**Path Parameters:**
- `id`: User ID (UUID)

**Request Body:**
```json
{
  "new_password": "string"
}
```

**Response:**
- **200 OK:** Password reset successfully
  ```json
  {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "display_name": "User Name",
    "type": "USER",
    "is_required_reset_password": false,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
  ```

- **404 Not Found:** User not found
  ```json
  {
    "code": "USER_NOT_FOUND",
    "message": "User not found"
  }
  ```

- **401 Unauthorized:** Invalid or missing authentication token

**Validation:**
- `new_password`: Required

### 3. Project Search and Filtering

**Endpoint:** `GET /projects`

**Description:** Enhanced project listing with combined search and filtering capabilities.

**Authentication:** Required (Bearer token)

**Query Parameters:**
- `search`: Search term (searches in project name, contact name, and contact email)
- `ministry_id`: Filter by ministry ID
- `department_id`: Filter by department ID
- `division_id`: Filter by division ID
- `created_by_id`: Filter by creator user ID
- `page`: Page number for pagination
- `limit`: Number of items per page
- `order_by`: Sorting order

**Examples:**
```
GET /projects?search=infrastructure
GET /projects?ministry_id=uuid&department_id=uuid
GET /projects?search=project&ministry_id=uuid&page=1&limit=10
```

**Response:**
- **200 OK:** Paginated project list
  ```json
  {
    "data": [
      {
        "id": "project-uuid",
        "name": "Project Name",
        "contact_name": "Contact Person",
        "contact_email": "<EMAIL>",
        "ministry": { "id": "uuid", "name_th": "Ministry Name" },
        "department": { "id": "uuid", "name_th": "Department Name" },
        "division": { "id": "uuid", "name_th": "Division Name" },
        "created_by": { "id": "uuid", "email": "<EMAIL>" }
      }
    ],
    "meta": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "total_pages": 10
    }
  }
  ```

## Implementation Details

### Security Features

1. **Password Hashing:** All passwords are hashed using bcrypt before storage
2. **Current Password Verification:** The change password endpoint verifies the current password before allowing changes
3. **Authentication Required:** Both endpoints require valid authentication tokens
4. **Password Reset Flag:** The reset password endpoint automatically sets `is_required_reset_password` to `false`

### Database Changes

The implementation uses the existing `is_required_reset_password` field in the users table:
- When a user's password is reset by an admin, this flag is set to `false`
- This can be used to track whether a user needs to change their password on next login

### Project Search Implementation

1. **Combined Filtering:** All filters can be used together in a single request
2. **Search Functionality:** Uses ILIKE for case-insensitive search across:
   - Project name
   - Contact name
   - Contact email
3. **Performance:** Leverages database indexes for efficient filtering
4. **Backward Compatibility:** Maintains existing pagination behavior when no filters are applied

### Error Handling

Both endpoints include comprehensive error handling for:
- Invalid authentication tokens
- User not found scenarios
- Password hashing failures
- Database operation failures
- Invalid current password (for change password endpoint)

### Testing

A Postman collection has been created at `postman-collections/password-endpoints.json` with:
- Example requests for both endpoints
- Test scripts to validate responses
- Sample success and error responses

## Usage Examples

### Change Own Password
```bash
curl -X POST http://localhost:8080/me/change-password \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "current_password": "oldpassword123",
    "new_password": "newpassword123"
  }'
```

### Reset User Password (Admin)
```bash
curl -X POST http://localhost:8080/users/USER_ID/reset-password \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "new_password": "resetpassword123"
  }'
```

### Search Projects
```bash
# Search by term
curl -X GET "http://localhost:8080/projects?search=infrastructure" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Filter by ministry
curl -X GET "http://localhost:8080/projects?ministry_id=MINISTRY_UUID" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Combined search and filters
curl -X GET "http://localhost:8080/projects?search=project&ministry_id=MINISTRY_UUID&department_id=DEPT_UUID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Files Modified/Created

### New Files:
- `docs/password-endpoints.md` - This documentation
- `postman-collections/password-endpoints.json` - Postman collection for testing

### Modified Files:

**Password Management:**
- `requests/user_create.request.go` - Added ChangePassword and ResetPassword request structures
- `services/user.dto.go` - Added ResetPasswordPayload
- `services/user.service.go` - Added ResetPassword method and interface
- `services/me.service.go` - Added ChangePassword method and ChangePasswordPayload
- `modules/me/me.controller.go` - Added ChangePassword controller method
- `modules/me/me.http.go` - Added change password route
- `modules/user/user.controller.go` - Added ResetPassword controller method
- `modules/user/user.http.go` - Added reset password route

**Project Search & Filtering:**
- `services/project.service.go` - Replaced individual filter methods with combined PaginationWithFilters method, added ProjectFilters struct
- `repo/project.repo.go` - Added ProjectBySearch function for text search
- `modules/project/project.controller.go` - Simplified controller to use combined filtering approach
