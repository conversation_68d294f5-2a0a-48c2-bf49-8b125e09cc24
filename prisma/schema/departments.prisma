model departments {
  id            String      @id @default(uuid()) @db.Uuid
  created_at    DateTime    @default(now())
  updated_at    DateTime    @updatedAt
  deleted_at    DateTime?
  ministry_id   String      @db.Uuid
  name_th       String      @unique
  name_en       String?     @unique
  short_name_th String?     @unique
  short_name_en String?     @unique
  code          Int?
  ministry      ministries  @relation(fields: [ministry_id], references: [id], onDelete: Cascade)
  divisions     divisions[]

  @@index([ministry_id])
  @@index([name_th])
}
