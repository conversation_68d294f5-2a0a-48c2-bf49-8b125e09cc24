/*
  Warnings:

  - Added the required column `department_id` to the `divisions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `ministry_id` to the `divisions` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."divisions" ADD COLUMN     "department_id" UUID NOT NULL,
ADD COLUMN     "ministry_id" UUID NOT NULL;

-- AddForeignKey
ALTER TABLE "public"."divisions" ADD CONSTRAINT "divisions_ministry_id_fkey" FOREIGN KEY ("ministry_id") REFERENCES "public"."ministries"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."divisions" ADD CONSTRAINT "divisions_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id") ON DELETE CASCADE ON UPDATE CASCADE;
