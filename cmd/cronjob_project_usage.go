package cmd

import (
	"fmt"
	"os"

	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

func CronjobProjectUsageRun() {
	env := core.NewEnv()

	db, err := core.NewDatabase(env.Config()).Connect()
	if err != nil {
		fmt.Fprintf(os.Stderr, "db: %v", err)
		os.Exit(1)
	}

	e := core.NewContext(&core.ContextOptions{
		DB:  db,
		ENV: env,
	})

	e.Log().Info("CreateJob AgentBilling is process...")

	agbSvc := services.NewProjectUsageService(e)
	agbSvc.GetUsage()

	e.Log().Info("CronjobProjectUsageRun done")
}
