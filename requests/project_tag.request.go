package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type ProjectTagCreate struct {
	core.BaseValidator
	Name  *string `json:"name"`
	Color *string `json:"color"`
}

func (r *ProjectTagCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.Is<PERSON>tr<PERSON>equired(r.Color, "color"))

	return r.Error()
}

type ProjectTagUpdate struct {
	core.BaseValidator
	Name  *string `json:"name"`
	Color *string `json:"color"`
}

func (r *ProjectTagUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.IsStrRequired(r.Color, "color"))

	return r.Error()
}
