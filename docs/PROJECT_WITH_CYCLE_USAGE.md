# Project WithCycleUsage Filter

## Overview

The `with_cycle_usage` parameter allows clients to conditionally include cycle usage data when fetching projects. This provides better performance when cycle usage data is not needed and ensures data is only loaded when required.

## API Usage

### Endpoint
```
GET /projects
```

### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `with_cycle_usage` | boolean | `false` | Include current cycle usage data in the response |

### Examples

#### Without Cycle Usage Data (Default)
```bash
curl -X GET "http://localhost:3001/projects?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### With Cycle Usage Data
```bash
curl -X GET "http://localhost:3001/projects?with_cycle_usage=true&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Combined with Other Filters
```bash
curl -X GET "http://localhost:3001/projects?with_cycle_usage=true&status=ACTIVE&provider_type=HWC" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Response Structure

### Without Cycle Usage (`with_cycle_usage=false` or not specified)
```json
{
  "data": [
    {
      "id": "project-id",
      "name": "Project Name",
      "code": "PROJ001",
      "status": "ACTIVE",
      "provider_type": "HWC",
      "created_by": { ... },
      "updated_by": { ... },
      "organization": { ... },
      "project_tags": [ ... ],
      "project_items": [ ... ]
      // project_usage field is NOT included
    }
  ],
  "pagination": { ... }
}
```

### With Cycle Usage (`with_cycle_usage=true`)
```json
{
  "data": [
    {
      "id": "project-id",
      "name": "Project Name",
      "code": "PROJ001",
      "status": "ACTIVE",
      "provider_type": "HWC",
      "created_by": { ... },
      "updated_by": { ... },
      "organization": { ... },
      "project_tags": [ ... ],
      "project_items": [ ... ],
      "project_usage": {
        "id": "usage-id",
        "project_id": "project-id",
        "cycle_id": "cycle-id",
        "amount": 1500.50,
        "timestamp": "2025-01-15T10:30:00Z",
        "cycle": {
          "id": "cycle-id",
          "name": "January 2025",
          "status": "current",
          "cycle_start_date": "2025-01-01",
          "cycle_end_date": "2025-01-31"
        }
      }
    }
  ],
  "pagination": { ... }
}
```

## Implementation Details

### Repository Layer
- `ProjectWithAllRelationsConditional(withCycleUsage bool)`: Conditionally includes cycle usage data
- `ProjectWithCycleUsage()`: Specifically loads current cycle usage data
- Uses optimized SQL query to get only the latest usage for the current cycle

### Service Layer
- `ProjectFilters.WithCycleUsage`: Boolean field to control cycle usage inclusion
- Passed to repository layer for conditional data loading

### Controller Layer
- Parses `with_cycle_usage` query parameter
- Converts string to boolean (only "true" is considered true)
- Defaults to `false` if not specified or invalid value

## Performance Considerations

- **Without cycle usage**: Faster queries, smaller response payload
- **With cycle usage**: Additional JOIN operations, larger response payload
- Use `with_cycle_usage=true` only when cycle usage data is actually needed
- The query is optimized to fetch only the latest usage per project for the current cycle

## Use Cases

### When to use `with_cycle_usage=true`
- Dashboard views showing current usage
- Budget monitoring interfaces
- Usage analytics and reporting
- Project overview with current consumption

### When to use `with_cycle_usage=false` (default)
- Simple project listings
- Project selection dropdowns
- Administrative project management
- When only basic project information is needed

## Compatibility

- Backward compatible: existing API calls without the parameter work unchanged
- Can be combined with all existing filters and sorting options
- Works with pagination, search, and all other project filtering features
