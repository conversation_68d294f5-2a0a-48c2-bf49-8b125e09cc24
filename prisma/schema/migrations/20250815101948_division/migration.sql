/*
  Warnings:

  - You are about to drop the column `department_id` on the `divisions` table. All the data in the column will be lost.
  - You are about to drop the column `ministry_id` on the `divisions` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."divisions" DROP CONSTRAINT "divisions_department_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."divisions" DROP CONSTRAINT "divisions_ministry_id_fkey";

-- DropIndex
DROP INDEX "public"."divisions_department_id_idx";

-- DropIndex
DROP INDEX "public"."divisions_ministry_id_idx";

-- DropIndex
DROP INDEX "public"."projects_name_code_status_idx";

-- AlterTable
ALTER TABLE "public"."divisions" DROP COLUMN "department_id",
DROP COLUMN "ministry_id";

-- AlterTable
ALTER TABLE "public"."projects" ADD COLUMN     "division_id" UUID;

-- CreateIndex
CREATE INDEX "projects_name_code_status_division_id_idx" ON "public"."projects"("name", "code", "status", "division_id");

-- AddForeignKey
ALTER TABLE "public"."projects" ADD CONSTRAINT "projects_division_id_fkey" FOREIGN KEY ("division_id") REFERENCES "public"."divisions"("id") ON DELETE SET NULL ON UPDATE CASCADE;
