import { PrismaClient } from "@prisma/client";
import * as bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function user() {
  // Hash the password using bcrypt (same as <PERSON>'s bcrypt.DefaultCost which is 10)
  const hashedPassword = await bcrypt.hash("12345678", 10);

  // Check if admin user already exists
  const existingAdmin = await prisma.users.findUnique({
    where: { email: "<EMAIL>" }
  });

  if (existingAdmin) {
    console.log("Admin user already exists, skipping creation");
    return;
  }

  // Create admin user
  const adminUser = await prisma.users.create({
    data: {
      email: "<EMAIL>",
      password: hashedPassword,
      display_name: "admin",
      type: "ADMIN",
    }
  });

  console.log("Admin user created:", {
    id: adminUser.id,
    email: adminUser.email,
    display_name: adminUser.display_name
  });
}

// [
//   {
//     "ลำดับ": 1,
//     "งวดที่": 2,
//     "กระทรวง": "กระทรวงศึกษาธิการ",
//     "กรม": "สำนักงานคณะกรรมการการศึกษาขั้นพื้นฐาน",
//     "หน่วยงาน": "สำนักงานเขตพื้นที่การศึกษาประถมศึกษากำแพงเพชร เขต 2",
//     "รูปแบบการใช้บริการ": "New CMS",
//     "Domain Name ที่ขอใช้บริการ": "kpp2.go.th",
//     "Domain Name ที่ให้บริการ *": "* kpp2.cms.opendata.go.th",
//     "Domain Name ที่เปลี่ยนแปลง": "",
//     "วันที่ส่งมอบ User Account": "17 ก.พ. 2568",
//     "วันที่เริ่มต้นใช้งาน": "19 ก.พ. 2568",
//     "วันที่สิ้นสุดการใช้งาน (1ปี)": "18 ก.พ. 2569",
//     "หมายเหตุ": "",
//     "opendata": 1,
//     "หน่วยงาน__1": "",
//     "cloudfare ของหน่วยงานเอง": ""
//   },
//   {
//     "ลำดับ": 2,
//     "งวดที่": 2,
//     "กระทรวง": "กระทรวงสาธารณสุข",
//     "กรม": "สำนักงานปลัดกระทรวงสาธารณสุข",
//     "หน่วยงาน": "โรงพยาบาลหนองหิน",
//     "รูปแบบการใช้บริการ": "New CMS",
//     "Domain Name ที่ขอใช้บริการ": "nonghinhospital.moph.go.th",
//     "Domain Name ที่ให้บริการ *": "* nonghinhospital.cms.opendata.go.th",
//     "Domain Name ที่เปลี่ยนแปลง": "",
//     "วันที่ส่งมอบ User Account": "17 ก.พ. 2568",
//     "วันที่เริ่มต้นใช้งาน": "19 ก.พ. 2568",
//     "วันที่สิ้นสุดการใช้งาน (1ปี)": "18 ก.พ. 2569",
//     "หมายเหตุ": "",
//     "opendata": 1,
//     "หน่วยงาน__1": "",
//     "cloudfare ของหน่วยงานเอง": ""
//   },
//   {
//     "ลำดับ": 3,
//     "งวดที่": 2,
//     "กระทรวง": "กระทรวงเกษตรและสหกรณ์",
//     "กรม": "สำนักงานสภาเกษตรกรแห่งชาติ",
//     "หน่วยงาน": "สำนักงานสภาเกษตรกรแห่งชาติ",
//     "รูปแบบการใช้บริการ": "New CMS",
//     "Domain Name ที่ขอใช้บริการ": "nfc.or.th",
//     "Domain Name ที่ให้บริการ *": "* nfc.cms.opendata.go.th",
//     "Domain Name ที่เปลี่ยนแปลง": "",
//     "วันที่ส่งมอบ User Account": "17 ก.พ. 2568",
//     "วันที่เริ่มต้นใช้งาน": "19 ก.พ. 2568",
//     "วันที่สิ้นสุดการใช้งาน (1ปี)": "18 ก.พ. 2569",
//     "หมายเหตุ": "",
//     "opendata": 1,
//     "หน่วยงาน__1": "",
//     "cloudfare ของหน่วยงานเอง": ""
//   },
// ]
function cmsProject() {
  return user();
}
user()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
