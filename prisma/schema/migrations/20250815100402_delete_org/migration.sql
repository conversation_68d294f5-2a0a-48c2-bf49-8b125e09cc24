/*
  Warnings:

  - You are about to drop the column `department_id` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `division_id` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `ministry_id` on the `projects` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."projects" DROP CONSTRAINT "projects_department_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."projects" DROP CONSTRAINT "projects_division_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."projects" DROP CONSTRAINT "projects_ministry_id_fkey";

-- DropIndex
DROP INDEX "public"."projects_name_ministry_id_department_id_division_id_code_st_idx";

-- AlterTable
ALTER TABLE "public"."projects" DROP COLUMN "department_id",
DROP COLUMN "division_id",
DROP COLUMN "ministry_id";

-- CreateIndex
CREATE INDEX "projects_name_code_status_idx" ON "public"."projects"("name", "code", "status");
