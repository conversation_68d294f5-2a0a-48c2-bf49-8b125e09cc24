# Project Cycles API Documentation

## Overview
The Project Cycles API provides endpoints for retrieving all billing cycles associated with a specific project, along with their latest usage information. This endpoint is useful for getting a comprehensive view of all cycles that have usage data for a particular project.

## Authentication
All endpoints require authentication using Bear<PERSON> token in the Authorization header:
```
Authorization: Bearer <your_access_token>
```

## Endpoints

### 1. Get Project Cycles
**GET** `/projects/{id}/cycles`

Retrieves all billing cycles for a specific project that have usage data, along with their latest usage information.

#### Path Parameters
- `id`: The UUID of the project

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search query (searches in cycle ID, start date, and end date)
- `order_by` (optional): Order by field (default: "cycle_start_date DESC")

#### Example Requests

##### Get All Cycles for a Project
```bash
GET /projects/550e8400-e29b-41d4-a716-************/cycles
```

##### Get Cycles with Pagination
```bash
GET /projects/550e8400-e29b-41d4-a716-************/cycles?page=1&limit=5
```

##### Get Cycles with Search
```bash
GET /projects/550e8400-e29b-41d4-a716-************/cycles?q=2024
```

##### Get Cycles with Custom Ordering
```bash
GET /projects/550e8400-e29b-41d4-a716-************/cycles?order_by=cycle_start_date ASC
```

#### Example Response
```json
{
  "data": [
    {
      "id": "cycle-uuid-1",
      "cycle_start_date": "2024-01-01T00:00:00Z",
      "cycle_end_date": "2024-01-31T23:59:59Z",
      "status": "completed",
      "hour_count": 744,
      "total_cost": 15000.50,
      "total_official_cost": 14500.00,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-02-01T00:00:00Z",
      "project_usages": [
        {
          "id": "usage-uuid-1",
          "project_id": "550e8400-e29b-41d4-a716-************",
          "amount": 1250.75,
          "official_amount": 1200.00,
          "hour_count": 62,
          "cycle_id": "cycle-uuid-1",
          "timestamp": "2024-01-31T23:59:59Z",
          "created_at": "2024-01-31T23:59:59Z",
          "updated_at": "2024-01-31T23:59:59Z",
          "project": {
            "id": "550e8400-e29b-41d4-a716-************",
            "name": "Digital Transformation Project",
            "code": "DTP-2024-001",
            "status": "ACTIVE"
          }
        }
      ]
    },
    {
      "id": "cycle-uuid-2",
      "cycle_start_date": "2024-02-01T00:00:00Z",
      "cycle_end_date": "2024-02-29T23:59:59Z",
      "status": "current",
      "hour_count": 696,
      "total_cost": 18500.25,
      "total_official_cost": 18000.00,
      "created_at": "2024-02-01T00:00:00Z",
      "updated_at": "2024-02-29T12:00:00Z",
      "project_usages": [
        {
          "id": "usage-uuid-2",
          "project_id": "550e8400-e29b-41d4-a716-************",
          "amount": 1850.25,
          "official_amount": 1800.00,
          "hour_count": 74,
          "cycle_id": "cycle-uuid-2",
          "timestamp": "2024-02-29T12:00:00Z",
          "created_at": "2024-02-29T12:00:00Z",
          "updated_at": "2024-02-29T12:00:00Z",
          "project": {
            "id": "550e8400-e29b-41d4-a716-************",
            "name": "Digital Transformation Project",
            "code": "DTP-2024-001",
            "status": "ACTIVE"
          }
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 2,
    "total_pages": 1
  }
}
```

#### Response Fields

##### Cycle Object
- `id`: UUID - Unique identifier for the cycle
- `cycle_start_date`: DateTime - Start date of the billing cycle
- `cycle_end_date`: DateTime - End date of the billing cycle
- `status`: String - Status of the cycle (e.g., "current", "completed", "pending")
- `hour_count`: Integer - Total hours in the cycle
- `total_cost`: Float - Total cost for the cycle
- `total_official_cost`: Float - Total official cost for the cycle
- `created_at`: DateTime - When the cycle was created
- `updated_at`: DateTime - When the cycle was last updated
- `project_usages`: Array - Array of project usage records for this cycle and project

##### Project Usage Object (within project_usages)
- `id`: UUID - Unique identifier for the usage record
- `project_id`: UUID - ID of the project
- `amount`: Float - Usage amount
- `official_amount`: Float - Official usage amount
- `hour_count`: Integer - Number of hours used
- `cycle_id`: UUID - ID of the billing cycle
- `timestamp`: DateTime - When the usage was recorded
- `created_at`: DateTime - When the record was created
- `updated_at`: DateTime - When the record was last updated
- `project`: Object - Basic project information

#### Error Responses

##### 404 Not Found - Project Not Found
```json
{
  "error": "Project not found",
  "message": "The specified project does not exist",
  "status": 404
}
```

##### 401 Unauthorized
```json
{
  "error": "Unauthorized",
  "message": "Authentication required",
  "status": 401
}
```

##### 400 Bad Request - Invalid Project ID
```json
{
  "error": "Invalid project ID",
  "message": "The provided project ID is not a valid UUID",
  "status": 400
}
```

## Use Cases

1. **Project Billing History**: Get a complete view of all billing cycles for a project
2. **Usage Trend Analysis**: Analyze usage patterns across different billing cycles
3. **Cost Tracking**: Track costs and usage over time for budget planning
4. **Reporting**: Generate reports showing project usage across multiple cycles

## Notes

- Only cycles that have usage data for the specified project will be returned
- The `project_usages` array will only contain usage records for the specified project
- Results are ordered by cycle start date in descending order by default (most recent first)
- Search functionality works on cycle ID, start date, and end date fields
- All datetime fields are returned in ISO 8601 format with UTC timezone
