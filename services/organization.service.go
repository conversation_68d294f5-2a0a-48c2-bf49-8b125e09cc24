package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IOrganizationService interface {
	Create(input *OrganizationCreatePayload) (*models.Organization, core.IError)
	Update(id string, input *OrganizationUpdatePayload) (*models.Organization, core.IError)
	Find(id string) (*models.Organization, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Organization], core.IError)
	Delete(id string) core.IError
	FindByMinistry(ministryID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Organization], core.IError)
	FindByDepartment(departmentID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Organization], core.IError)
}

type organizationService struct {
	ctx core.IContext
}

func (s organizationService) Create(input *OrganizationCreatePayload) (*models.Organization, core.IError) {
	organization := &models.Organization{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		NameTh:              input.NameTh,
		NameEn:              utils.ToPointer(input.NameEn),
		Code:                input.Code,
		ShortNameTh:         input.ShortNameTh,
		ShortNameEn:         input.ShortNameEn,
	}

	ierr := repo.Organization(s.ctx).Create(organization)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(organization.ID)
}

func (s organizationService) Update(id string, input *OrganizationUpdatePayload) (*models.Organization, core.IError) {
	organization, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if input.NameTh != "" {
		organization.NameTh = input.NameTh
	}
	if input.NameEn != "" {
		organization.NameEn = utils.ToPointer(input.NameEn)
	}
	if input.Code != nil {
		organization.Code = input.Code
	}
	if input.ShortNameTh != nil {
		organization.ShortNameTh = input.ShortNameTh
	}
	if input.ShortNameEn != nil {
		organization.ShortNameEn = input.ShortNameEn
	}

	// Update timestamp
	organization.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repo.Organization(s.ctx).Where("id = ?", id).Updates(organization)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(organization.ID)
}

func (s organizationService) Find(id string) (*models.Organization, core.IError) {
	return repo.Organization(s.ctx).FindOne("id = ?", id)
}

func (s organizationService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Organization], core.IError) {
	return repo.Organization(
		s.ctx,
		repo.OrganizationOrderBy(pageOptions),
		repo.OrganizationWithSearch(pageOptions.Q)).
		Pagination(pageOptions)
}

func (s organizationService) FindByMinistry(ministryID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Organization], core.IError) {
	return repo.Organization(s.ctx, repo.OrganizationOrderBy(pageOptions)).Pagination(pageOptions)
}

func (s organizationService) FindByDepartment(departmentID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Organization], core.IError) {
	return repo.Organization(s.ctx, repo.OrganizationOrderBy(pageOptions)).Pagination(pageOptions)
}

func (s organizationService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Organization(s.ctx).Delete("id = ?", id)
}

func NewOrganizationService(ctx core.IContext) IOrganizationService {
	return &organizationService{ctx: ctx}
}
