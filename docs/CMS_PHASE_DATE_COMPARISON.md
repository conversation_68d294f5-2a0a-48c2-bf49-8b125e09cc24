# CMS Project Phase Date Comparison Implementation

This document describes the comprehensive date comparison functionality implemented for CMS Project Phases.

## Overview

The CMS Project Phase date comparison system provides:
- Date validation (ensuring end_date is after start_date)
- Phase status determination based on current date
- Duration calculations
- Overlap detection between phases
- Date-based filtering and querying

## Model Methods (`models/cms_project_phase.model.go`)

### Date Validation
- `IsValidDateRange() bool`: Checks if end_date is after or equal to start_date
- Returns false if either date is nil

### Duration Calculations
- `GetDuration() time.Duration`: Returns the duration between start and end dates
- `GetDurationInDays() int`: Returns duration in days (rounded down)

### Phase Status Methods
- `IsCurrentPhase() bool`: Checks if the phase is currently active (today is between start and end dates)
- `IsUpcoming() bool`: Checks if the phase hasn't started yet
- `IsCompleted() bool`: Checks if the phase has ended

### Overlap Detection
- `OverlapsWith(other CMSProjectPhase) bool`: Checks if two phases overlap in time

### Time Until Methods
- `DaysUntilStart() int`: Returns days until phase starts (0 if already started, -1 if no start date)
- `DaysUntilEnd() int`: Returns days until phase ends (0 if already ended, -1 if no end date)

## Request Validation (`requests/cms_project_phase.request.go`)

### Date Range Validation
Both `CMSProjectPhaseCreate` and `CMSProjectPhaseUpdate` now validate:
- Individual date format validation using `IsDate()`
- Cross-field validation ensuring end_date is after start_date
- Uses `IsDateAfter()` validation method

### Validation Rules
- start_date and end_date are required
- Dates must be in YYYY-MM-DD format
- end_date must be after start_date

## Service Layer (`services/cms_project_phase.service.go`)

### New Service Methods
- `FindCurrentPhases()`: Returns all currently active phases
- `FindUpcomingPhases()`: Returns all phases that haven't started
- `FindCompletedPhases()`: Returns all phases that have ended
- `FindOverlappingPhases(projectID)`: Returns overlapping phases within a project
- `FindPhasesByDateRange(startDate, endDate)`: Returns phases within or overlapping a date range

### Query Logic
- **Current phases**: `start_date <= NOW() AND end_date >= NOW()`
- **Upcoming phases**: `start_date > NOW()`
- **Completed phases**: `end_date < NOW()`
- **Overlapping phases**: Complex SQL query to detect overlaps within the same project
- **Date range**: Phases that start, end, or span the given date range

## Repository Layer (`repo/cms_project_phase.repo.go`)

### New Repository Options
- `CMSProjectPhaseByDateRange(startDate, endDate)`: Filter by date range
- `CMSProjectPhaseCurrent()`: Filter for current phases
- `CMSProjectPhaseUpcoming()`: Filter for upcoming phases
- `CMSProjectPhaseCompleted()`: Filter for completed phases
- `CMSProjectPhaseOverlapping(projectID)`: Filter for overlapping phases

## Controller Layer (`modules/cms/cms_project_phase.controller.go`)

### New Endpoints
- `FindCurrent()`: GET /cms/phases/current
- `FindUpcoming()`: GET /cms/phases/upcoming
- `FindCompleted()`: GET /cms/phases/completed
- `FindOverlapping()`: GET /cms/projects/:project_id/phases/overlapping
- `FindByDateRange()`: GET /cms/phases/date-range?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD

### Error Handling
- Validates date format for date range queries
- Returns appropriate HTTP status codes
- Provides clear error messages for invalid inputs

## API Endpoints

### Date-Based Phase Queries

#### GET /cms/phases/current
Returns all currently active phases across all projects.

#### GET /cms/phases/upcoming
Returns all phases that haven't started yet.

#### GET /cms/phases/completed
Returns all phases that have ended.

#### GET /cms/phases/date-range
Query parameters:
- `start_date` (required): YYYY-MM-DD format
- `end_date` (required): YYYY-MM-DD format

Returns phases that overlap with the specified date range.

#### GET /cms/projects/:project_id/phases/overlapping
Returns phases within a specific project that overlap with each other.

### Response Format
All endpoints return arrays of CMSProjectPhase objects with full relations:

```json
[
  {
    "id": "uuid",
    "cms_project_id": "uuid",
    "phase": 1,
    "work_phase": 1,
    "start_date": "2024-01-01T00:00:00Z",
    "end_date": "2024-03-31T00:00:00Z",
    "type": "NEW",
    "file_url": "https://example.com/file.pdf",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "project": {
      "id": "uuid",
      "name": "Project Name",
      ...
    }
  }
]
```

## Usage Examples

### Model Usage
```go
phase := models.CMSProjectPhase{...}

// Check if phase is valid
if !phase.IsValidDateRange() {
    // Handle invalid date range
}

// Get phase status
if phase.IsCurrentPhase() {
    // Phase is currently active
} else if phase.IsUpcoming() {
    // Phase hasn't started
} else if phase.IsCompleted() {
    // Phase has ended
}

// Calculate duration
days := phase.GetDurationInDays()
daysUntilEnd := phase.DaysUntilEnd()
```

### Service Usage
```go
service := services.NewCMSProjectPhaseService(ctx)

// Get current phases
currentPhases, err := service.FindCurrentPhases()

// Find overlapping phases
overlapping, err := service.FindOverlappingPhases("project-id")

// Find phases in date range
phases, err := service.FindPhasesByDateRange(startDate, endDate)
```

## Testing

Use the provided `test_cms_phase_dates.http` file to test all date comparison functionality:
- Valid and invalid date range creation/updates
- Current, upcoming, and completed phase queries
- Date range filtering
- Overlap detection
- Error handling for invalid inputs

## Performance Considerations

- Database indexes on `start_date`, `end_date`, and `cms_project_id` for optimal query performance
- Efficient SQL queries for overlap detection
- Proper use of database-level date comparisons

## Future Enhancements

Potential improvements:
- Phase timeline visualization
- Automatic phase status updates
- Phase dependency management
- Notification system for upcoming deadlines
- Bulk phase operations with date validation
