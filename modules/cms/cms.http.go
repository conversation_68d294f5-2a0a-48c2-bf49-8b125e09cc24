package cms

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/csp/csp-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewCMSHTTP(e *echo.Echo) {
	cmsProject := &CMSProjectController{}
	cmsProjectPhase := &CMSProjectPhaseController{}

	// CMS Project routes - all require authentication
	// Dashboard - all projects,project by status value, nearly expired projects 3 month
	e.GET("/cms/dashboard", core.WithHTTPContext(cmsProject.Dashboard), middleware.AuthMiddleware())
	e.GET("/cms/projects", core.WithHTTPContext(cmsProject.Pagination), middleware.AuthMiddleware())
	e.GET("/cms/projects/:id", core.WithHTTPContext(cmsProject.Find), middleware.AuthMiddleware())
	e.POST("/cms/projects", core.WithHTTPContext(cmsProject.Create), middleware.AuthMiddleware())
	e.PUT("/cms/projects/:id", core.WithHTTPContext(cmsProject.Update), middleware.AuthMiddleware())
	e.DELETE("/cms/projects/:id", core.WithHTTPContext(cmsProject.Delete), middleware.AuthMiddleware())
	e.POST("/cms/projects/:id/status", core.WithHTTPContext(cmsProject.UpdateStatus), middleware.AuthMiddleware())

	// CMS Project Phase routes - all require authentication
	e.GET("/cms/projects/:project_id/phases", core.WithHTTPContext(cmsProjectPhase.FindByProject), middleware.AuthMiddleware())
	e.POST("/cms/projects/:project_id/phases", core.WithHTTPContext(cmsProjectPhase.Create), middleware.AuthMiddleware())
	e.PUT("/cms/projects/:project_id/phases/:id", core.WithHTTPContext(cmsProjectPhase.Update), middleware.AuthMiddleware())
	e.DELETE("/cms/projects/:project_id/phases/:id", core.WithHTTPContext(cmsProjectPhase.Delete), middleware.AuthMiddleware())
	e.GET("/cms/projects/:project_id/phases/:id", core.WithHTTPContext(cmsProjectPhase.Find), middleware.AuthMiddleware())
}
