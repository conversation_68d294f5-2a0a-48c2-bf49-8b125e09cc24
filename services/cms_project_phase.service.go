package services

import (
	"time"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ICMSProjectPhaseService interface {
	Create(input *CMSProjectPhaseCreatePayload) (*models.CMSProjectPhase, core.IError)
	Update(id string, input *CMSProjectPhaseUpdatePayload) (*models.CMSProjectPhase, core.IError)
	Find(id string) (*models.CMSProjectPhase, core.IError)
	FindByProject(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.CMSProjectPhase], core.IError)
	Delete(id string) core.IError
}

type cmsProjectPhaseService struct {
	ctx core.IContext
}

func (s cmsProjectPhaseService) Create(input *CMSProjectPhaseCreatePayload) (*models.CMSProjectPhase, core.IError) {
	startDate, _ := time.Parse(time.DateOnly, input.StartDate)
	endDate, _ := time.Parse(time.DateOnly, input.EndDate)
	cmsProjectPhase := &models.CMSProjectPhase{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		CMSProjectID:        input.CMSProjectID,
		Phase:               input.Phase,
		WorkPhase:           input.WorkPhase,
		StartDate:           &startDate,
		EndDate:             &endDate,
		Type:                models.CMSProjectType(input.Type),
		FileURL:             input.FileURL,
	}

	ierr := repo.CMSProjectPhase(s.ctx).Create(cmsProjectPhase)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	ierr = repo.CMSProject(s.ctx).Where("id = ?", input.CMSProjectID).Updates(map[string]interface{}{
		"phase_id": cmsProjectPhase.ID,
	})
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(cmsProjectPhase.ID)
}

func (s cmsProjectPhaseService) Update(id string, input *CMSProjectPhaseUpdatePayload) (*models.CMSProjectPhase, core.IError) {
	cmsProjectPhase, ierr := repo.CMSProjectPhase(s.ctx).FindOne("id = ?", id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	startDate, _ := time.Parse(time.DateOnly, input.StartDate)
	endDate, _ := time.Parse(time.DateOnly, input.EndDate)

	// Update fields
	cmsProjectPhase.Phase = input.Phase
	cmsProjectPhase.WorkPhase = input.WorkPhase
	cmsProjectPhase.StartDate = &startDate
	cmsProjectPhase.EndDate = &endDate
	cmsProjectPhase.Type = models.CMSProjectType(input.Type)
	cmsProjectPhase.FileURL = input.FileURL
	cmsProjectPhase.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repo.CMSProjectPhase(s.ctx).Where("id = ?", id).Updates(cmsProjectPhase)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(cmsProjectPhase.ID)
}

func (s cmsProjectPhaseService) Find(id string) (*models.CMSProjectPhase, core.IError) {
	return repo.CMSProjectPhase(s.ctx, repo.CMSProjectPhaseWithRelations()).FindOne("id = ?", id)
}

func (s cmsProjectPhaseService) FindByProject(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.CMSProjectPhase], core.IError) {
	return repo.CMSProjectPhase(
		s.ctx,
		repo.CMSProjectPhaseOrderBy(pageOptions),
		repo.CMSProjectPhaseByProject(projectID),
		repo.CMSProjectPhaseWithRelations()).
		Pagination(pageOptions)
}

func (s cmsProjectPhaseService) Delete(id string) core.IError {
	cmsProjectPhase, ierr := repo.CMSProjectPhase(s.ctx).FindOne("id = ?", id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	ierr = repo.CMSProjectPhase(s.ctx).Delete(cmsProjectPhase)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	lastPhase, ierr := repo.CMSProjectPhase(s.ctx).Where("cms_project_id = ?", cmsProjectPhase.CMSProjectID).Order("phase DESC").FindOne()
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return s.ctx.NewError(ierr, ierr)
	}

	if lastPhase == nil {
		return nil
	}

	ierr = repo.CMSProject(s.ctx).Where("id = ?", cmsProjectPhase.CMSProjectID).Updates(map[string]interface{}{
		"phase_id": lastPhase.ID,
	})
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return nil
}

func NewCMSProjectPhaseService(ctx core.IContext) ICMSProjectPhaseService {
	return &cmsProjectPhaseService{ctx: ctx}
}
