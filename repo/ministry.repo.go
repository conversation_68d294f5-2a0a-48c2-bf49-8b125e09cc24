package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type MinistryOption func(repository.IRepository[models.Ministry])

var Ministry = func(c core.IContext, options ...MinistryOption) repository.IRepository[models.Ministry] {
	r := repository.New[models.Ministry](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func MinistryOrderBy(pageOptions *core.PageOptions) MinistryOption {
	return func(c repository.IRepository[models.Ministry]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func MinistryWithDepartments() MinistryOption {
	return func(c repository.IRepository[models.Ministry]) {
		c.Preload("Departments")
	}
}

func MinistryWithSearch(search string) MinistryOption {
	if search == "" {
		return func(c repository.IRepository[models.Ministry]) {}
	}
	return func(c repository.IRepository[models.Ministry]) {
		searchPattern := "%" + search + "%"
		c.Where("name_th ILIKE ? OR name_en ILIKE ?", searchPattern, searchPattern)
	}
}
