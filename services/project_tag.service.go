package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type IProjectTagService interface {
	Create(projectId string, input *ProjectTagCreatePayload) (*models.ProjectTag, core.IError)
	Update(id string, input *ProjectTagUpdatePayload) (*models.ProjectTag, core.IError)
	Find(id string) (*models.ProjectTag, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.ProjectTag], core.IError)
	Delete(id string) core.IError
}

type projectTagService struct {
	ctx core.IContext
}

func (s projectTagService) Create(projectId string, input *ProjectTagCreatePayload) (*models.ProjectTag, core.IError) {
	projectSvc := NewProjectService(s.ctx)
	_, ierr := projectSvc.Find(projectId)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	projectTag := &models.ProjectTag{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		Name:                input.Name,
		Color:               input.Color,
		ProjectID:           projectId,
	}

	ierr = repo.ProjectTag(s.ctx).Create(projectTag)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(projectTag.ID)
}

func (s projectTagService) Update(id string, input *ProjectTagUpdatePayload) (*models.ProjectTag, core.IError) {
	projectTag, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		projectTag.Name = input.Name
	}
	if input.Color != "" {
		projectTag.Color = input.Color
	}

	ierr = repo.ProjectTag(s.ctx).Where("id = ?", id).Updates(projectTag)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(projectTag.ID)
}

func (s projectTagService) Find(id string) (*models.ProjectTag, core.IError) {
	return repo.ProjectTag(s.ctx).FindOne("id = ?", id)
}

func (s projectTagService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.ProjectTag], core.IError) {
	return repo.ProjectTag(
		s.ctx,
		repo.ProjectTagOrderBy(pageOptions),
		repo.ProjectTagDistinctByName(),
		repo.ProjectTagWithSearch(pageOptions.Q)).
		Pagination(pageOptions)
}

func (s projectTagService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.ProjectTag(s.ctx).Delete("id = ?", id)
}

func NewProjectTagService(ctx core.IContext) IProjectTagService {
	return &projectTagService{ctx: ctx}
}
