package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CMSProjectPhase struct {
	Phase     *int64  `json:"phase"`
	WorkPhase *int64  `json:"work_phase"`
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
	FileURL   *string `json:"file_url"`
}

type CMSProjectCreate struct {
	core.BaseValidator
	MinistryID   *string          `json:"ministry_id"`
	DepartmentID *string          `json:"department_id"`
	DivisionID   *string          `json:"division_id"`
	Name         *string          `json:"name"`
	Type         *string          `json:"type"`
	Domain       *string          `json:"domain"`
	ContactName  *string          `json:"contact_name"`
	ContactPhone *string          `json:"contact_phone"`
	ContactEmail *string          `json:"contact_email"`
	Remark       *string          `json:"remark"`
	Status       *string          `json:"status"`
	Phase        *CMSProjectPhase `json:"phase"`
}

func (r *CMSProjectCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.MinistryID, "ministry_id"))
	r.Must(r.IsStrRequired(r.DepartmentID, "department_id"))
	r.Must(r.IsStrRequired(r.DivisionID, "division_id"))
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.IsStrRequired(r.Type, "type"))
	r.Must(r.IsStrRequired(r.Domain, "domain"))
	r.Must(r.IsURL(r.Domain, "domain"))
	r.Must(r.IsStrRequired(r.ContactName, "contact_name"))
	r.Must(r.IsStrRequired(r.ContactPhone, "contact_phone"))
	r.Must(r.IsStrRequired(r.ContactEmail, "contact_email"))

	// Validate unique constraints
	r.Must(r.IsStrUnique(ctx, r.Name, models.CMSProject{}.TableName(), "name", "", "name"))
	r.Must(r.IsStrUnique(ctx, r.Domain, models.CMSProject{}.TableName(), "domain", "", "domain"))

	// Validate email format
	if r.ContactEmail != nil && utils.ToNonPointer(r.ContactEmail) != "" {
		r.Must(r.IsEmail(r.ContactEmail, "contact_email"))
	}

	// Validate type enum
	if r.Type != nil {
		r.Must(r.IsStrIn(r.Type,
			string(models.CMSProjectTypeNew)+"|"+string(models.CMSProjectTypeMigration),
			"type"))
	}

	// Validate status enum
	if r.Status != nil {
		r.Must(r.IsStrIn(r.Status,
			string(models.CMSProjectStatusDraft)+"|"+
				string(models.CMSProjectStatusNew)+"|"+
				string(models.CMSProjectStatusInProcess)+"|"+
				string(models.CMSProjectStatusActive)+"|"+
				string(models.CMSProjectStatusClose),
			"status"))
	}

	// Validate foreign key references
	if r.MinistryID != nil && utils.ToNonPointer(r.MinistryID) != "" {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	if r.DepartmentID != nil && utils.ToNonPointer(r.DepartmentID) != "" {
		r.Must(r.IsExists(ctx, r.DepartmentID, models.Department{}.TableName(), "id", "department_id"))
	}

	if r.DivisionID != nil && utils.ToNonPointer(r.DivisionID) != "" {
		r.Must(r.IsExists(ctx, r.DivisionID, models.Division{}.TableName(), "id", "division_id"))
	}

	if r.Phase != nil {
		r.Must(r.IsRequired(r.Phase, "phase"))
		r.Must(r.IsRequired(r.Phase.Phase, "phase.phase"))
		r.Must(r.IsRequired(r.Phase.WorkPhase, "phase.work_phase"))
		r.Must(r.IsRequired(r.Phase.StartDate, "phase.start_date"))
		r.Must(r.IsRequired(r.Phase.EndDate, "phase.end_date"))
		r.Must(r.IsURL(r.Phase.FileURL, "phase.file_url"))
		r.Must(r.IsDate(r.Phase.StartDate, "phase.start_date"))
		r.Must(r.IsDate(r.Phase.EndDate, "phase.end_date"))
		r.Must(r.IsDateAfter(r.Phase.EndDate, r.Phase.StartDate, "phase.end_date"))
	}

	return r.Error()
}

type CMSProjectUpdate struct {
	core.BaseValidator
	MinistryID   *string `json:"ministry_id"`
	DepartmentID *string `json:"department_id"`
	DivisionID   *string `json:"division_id"`
	Name         *string `json:"name"`
	Type         *string `json:"type"`
	Domain       *string `json:"domain"`
	ContactName  *string `json:"contact_name"`
	ContactPhone *string `json:"contact_phone"`
	ContactEmail *string `json:"contact_email"`
	Remark       *string `json:"remark"`
	Status       *string `json:"status"`
}

func (r *CMSProjectUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldName := ""
	oldDomain := ""

	cmsProject, _ := repo.CMSProject(cc).FindOne("id = ?", cc.Param("id"))
	if cmsProject != nil {
		oldName = cmsProject.Name
		oldDomain = cmsProject.Domain
	}

	// Validate unique constraints only if values are provided and different
	if r.Name != nil && utils.ToNonPointer(r.Name) != "" && utils.ToNonPointer(r.Name) != oldName {
		r.Must(r.IsStrUnique(ctx, r.Name, models.CMSProject{}.TableName(), "name", oldName, "name"))
	}

	if r.Domain != nil && utils.ToNonPointer(r.Domain) != "" && utils.ToNonPointer(r.Domain) != oldDomain {
		r.Must(r.IsStrUnique(ctx, r.Domain, models.CMSProject{}.TableName(), "domain", oldDomain, "domain"))
	}

	// Validate email format
	if r.ContactEmail != nil && utils.ToNonPointer(r.ContactEmail) != "" {
		r.Must(r.IsEmail(r.ContactEmail, "contact_email"))
	}

	r.Must(r.IsURL(r.Domain, "domain"))

	// Validate type enum
	if r.Type != nil && utils.ToNonPointer(r.Type) != "" {
		r.Must(r.IsStrIn(r.Type,
			string(models.CMSProjectTypeNew)+"|"+string(models.CMSProjectTypeMigration),
			"type"))
	}

	r.Must(r.IsStrIn(r.Status,
		string(models.CMSProjectStatusDraft)+"|"+
			string(models.CMSProjectStatusNew)+"|"+
			string(models.CMSProjectStatusInProcess)+"|"+
			string(models.CMSProjectStatusActive)+"|"+
			string(models.CMSProjectStatusClose),
		"status"))

	// Validate foreign key references
	if r.MinistryID != nil && utils.ToNonPointer(r.MinistryID) != "" {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	if r.DepartmentID != nil && utils.ToNonPointer(r.DepartmentID) != "" {
		r.Must(r.IsExists(ctx, r.DepartmentID, models.Department{}.TableName(), "id", "department_id"))
	}

	if r.DivisionID != nil && utils.ToNonPointer(r.DivisionID) != "" {
		r.Must(r.IsExists(ctx, r.DivisionID, models.Division{}.TableName(), "id", "division_id"))
	}

	return r.Error()
}

type CMSProjectStatusUpdate struct {
	core.BaseValidator
	Status *string `json:"status"`
}

func (r *CMSProjectStatusUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Status, "status"))

	r.Must(r.IsStrIn(r.Status,
		string(models.CMSProjectStatusDraft)+"|"+
			string(models.CMSProjectStatusNew)+"|"+
			string(models.CMSProjectStatusInProcess)+"|"+
			string(models.CMSProjectStatusActive)+"|"+
			string(models.CMSProjectStatusClose),
		"status"))

	return r.Error()
}
