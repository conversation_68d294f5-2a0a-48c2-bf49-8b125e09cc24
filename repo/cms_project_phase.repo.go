package repo

import (
	"time"

	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type CMSProjectPhaseOption func(repository.IRepository[models.CMSProjectPhase])

var CMSProjectPhase = func(c core.IContext, options ...CMSProjectPhaseOption) repository.IRepository[models.CMSProjectPhase] {
	r := repository.New[models.CMSProjectPhase](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func CMSProjectPhaseOrderBy(pageOptions *core.PageOptions) CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("phase DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func CMSProjectPhaseByProject(projectID string) CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		if projectID != "" {
			c.Where("cms_project_id = ?", projectID)
		}
	}
}

func CMSProjectPhaseByType(types []string) CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		if len(types) > 0 {
			c.Where("type IN ?", types)
		}
	}
}

func CMSProjectPhaseByPhase(phase int) CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		if phase > 0 {
			c.Where("phase = ?", phase)
		}
	}
}

func CMSProjectPhaseWithRelations() CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		c.Preload("Project")
	}
}

func CMSProjectPhaseByDateRange(startDate, endDate *time.Time) CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		if startDate != nil {
			c.Where("start_date >= ?", *startDate)
		}
		if endDate != nil {
			c.Where("end_date <= ?", *endDate)
		}
	}
}

func CMSProjectPhaseCurrent() CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		now := time.Now()
		c.Where("start_date <= ? AND end_date >= ?", now, now)
	}
}

func CMSProjectPhaseUpcoming() CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		now := time.Now()
		c.Where("start_date > ?", now)
	}
}

func CMSProjectPhaseCompleted() CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		now := time.Now()
		c.Where("end_date < ?", now)
	}
}

func CMSProjectPhaseOverlapping(projectID string) CMSProjectPhaseOption {
	return func(c repository.IRepository[models.CMSProjectPhase]) {
		if projectID != "" {
			c.Where("cms_project_id = ?", projectID).
				Where("EXISTS (SELECT 1 FROM cms_project_phases cp2 WHERE cp2.cms_project_id = cms_project_phases.cms_project_id AND cp2.id != cms_project_phases.id AND cp2.start_date < cms_project_phases.end_date AND cms_project_phases.start_date < cp2.end_date)")
		}
	}
}
