package services

import (
	"gitlab.finema.co/finema/csp/csp-api/helpers"
	"gitlab.finema.co/finema/csp/csp-api/models"   // Importing the models package
	"gitlab.finema.co/finema/csp/csp-api/repo"     // Importing the repo package
	core "gitlab.finema.co/finema/idin-core"       // Importing the core package
	"gitlab.finema.co/finema/idin-core/repository" // Importing the repository package
	"gitlab.finema.co/finema/idin-core/utils"
)

type IUserService interface { // Defining the IUserService interface
	Create(input *UserCreatePayload) (*models.User, core.IError)                                 // Method declaration for creating a user
	Update(id string, input *UserUpdatePayload) (*models.User, core.IError)                      // Method declaration for updating a user
	Find(id string) (*models.User, core.IError)                                                  // Method declaration for finding a user
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.User], core.IError) // Method declaration for pagination of users
	Delete(id string) core.IError                                                                // Method declaration for deleting a user
	ResetPassword(id string, input *ResetPasswordPayload) (*models.User, core.IError)            // Method declaration for resetting a user's password
}

type userService struct {
	ctx core.IContext // Struct for userService with a context field
}

func (s userService) Create(input *UserCreatePayload) (*models.User, core.IError) {
	// Hash the password
	hashedPassword, err := helpers.HashPassword(input.Password)
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  500,
			Code:    "PASSWORD_HASH_FAILED",
			Message: "Failed to hash password",
		}, core.Error{})
	}

	if input.Type == "" {
		input.Type = string(models.UserTypeUser)
	}

	// Implementation for creating a user
	user := &models.User{
		BaseModel:   models.NewBaseModel(),
		Email:       input.Email,
		Password:    hashedPassword,
		DisplayName: utils.ToPointer(input.DisplayName),
		Type:        models.UserType(input.Type), // Set default user type
	}

	ierr := repo.User(s.ctx).Create(user) // Calling the Create method of the repo.User repository
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Create method fails
	}

	return s.Find(user.ID) // Calling the Find method to retrieve the created user
}

func (s userService) Update(id string, input *UserUpdatePayload) (*models.User, core.IError) {
	// Implementation for updating a user
	user, ierr := s.Find(id) // Calling the Find method to retrieve the user to update
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Find method fails
	}

	// Update fields if provided (only DisplayName is available in the current User model)
	if input.DisplayName != "" {
		user.DisplayName = utils.ToPointer(input.DisplayName)
	}

	if input.Type != "" {
		user.Type = models.UserType(input.Type)
	}

	// Update timestamp
	user.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repo.User(s.ctx).Where("id = ?", id).Updates(user) // Calling the Updates method of the repo.User repository
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Updates method fails
	}

	return s.Find(user.ID) // Calling the Find method to retrieve the updated user
}

func (s userService) Find(id string) (*models.User, core.IError) {
	// Implementation for finding a user
	return repo.User(s.ctx).FindOne("id = ?", id) // Calling the FindOne method of the repo.User repository
}

func (s userService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.User], core.IError) {
	// Implementation for user pagination
	return repo.User(s.ctx, repo.UserOrderBy(pageOptions), repo.UserWithSearch(pageOptions.Q)).Pagination(pageOptions) // Calling the Pagination method of the repo.User repository
}

func (s userService) Delete(id string) core.IError {
	// Implementation for deleting a user
	_, ierr := s.Find(id) // Calling the Find method to check if the user exists
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr) // Returning an error if the Find method fails
	}

	return repo.User(s.ctx).Delete("id = ?", id) // Calling the Delete method of the repo.User repository
}

func (s userService) ResetPassword(id string, input *ResetPasswordPayload) (*models.User, core.IError) {
	// Implementation for resetting a user's password
	user, ierr := s.Find(id) // Calling the Find method to retrieve the user
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Find method fails
	}

	// Hash the new password
	hashedPassword, err := helpers.HashPassword(input.NewPassword)
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  500,
			Code:    "PASSWORD_HASH_FAILED",
			Message: "Failed to hash password",
		}, core.Error{})
	}

	// Update password and reset flag
	user.Password = hashedPassword
	user.IsRequiredResetPassword = false
	user.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repo.User(s.ctx).Where("id = ?", id).Updates(user) // Calling the Updates method of the repo.User repository
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Updates method fails
	}

	return s.Find(user.ID) // Calling the Find method to retrieve the updated user
}

func NewUserService(ctx core.IContext) IUserService {
	return &userService{ctx: ctx} // Creating a new instance of userService with the provided context
}
