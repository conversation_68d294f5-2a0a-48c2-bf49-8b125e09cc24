model projects {
  id                String              @id @default(uuid()) @db.Uuid
  name              String
  contact_name      String?
  contact_phone     String?
  contact_email     String?
  budget            Float?
  created_at        DateTime            @default(now())
  updated_at        DateTime            @default(now()) @updatedAt
  deleted_at        DateTime?
  created_by_id     String?             @db.Uuid
  deleted_by_id     String?             @db.Uuid
  updated_by_id     String?             @db.Uuid
  root_account_name String              @default("en3")
  account_holder    String              @default("Finema")
  account_name      String              @default("")
  account_id        String              @default("")
  code              String              @unique
  organization_id   String?             @db.Uuid
  remark            String?             @default("")
  provider_type     ProjectProviderType @default(HWC)
  psa_email         String              @default("")
  status            ProjectStatus       @default(DRAFT)
  project_usages    project_usages[]
  creator           users?              @relation("ProjectCreator", fields: [created_by_id], references: [id])
  deleter           users?              @relation("ProjectDeleter", fields: [deleted_by_id], references: [id])

  updater       users?          @relation("ProjectUpdater", fields: [updated_by_id], references: [id])
  organizations organizations?  @relation(fields: [organization_id], references: [id])
  project_items project_items[]
  project_tags  project_tags[]

  @@index([name, code, status, organization_id, provider_type, budget])
}

enum ProjectStatus {
  DRAFT
  ACTIVE
  CLOSED
}

enum ProjectProviderType {
  HWC
  AWS
  CHM
}
