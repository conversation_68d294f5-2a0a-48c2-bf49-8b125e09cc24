model ministries {
  id            String        @id @default(uuid()) @db.Uuid
  created_at    DateTime      @default(now())
  updated_at    DateTime      @updatedAt
  deleted_at    DateTime?
  name_th       String        @unique
  name_en       String?       @unique
  code          Int?          @unique
  short_name_th String?       @unique
  short_name_en String?       @unique
  departments   departments[]
  divisions     divisions[]

  @@index([name_th])
}
