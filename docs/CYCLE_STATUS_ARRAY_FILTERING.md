# Cycle Status Array Filtering

## Overview

The cycle API now supports filtering cycles by multiple status values. This feature allows you to retrieve cycles that match any of the specified statuses using a single API call.

## API Endpoint

```
GET /cycles?status=status1,status2,status3
```

## Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `status` | string | Comma-separated list of status values to filter by | `current,unbilled` |

## Behavior

### OR Operation
The status filtering uses an **OR** operation, meaning cycles that match **ANY** of the specified statuses will be included in the results.

**Example:**
- Request: `GET /cycles?status=current,unbilled`
- Result: Cycles that have status "current" OR "unbilled"

### Backward Compatibility
Single status filtering continues to work exactly as before:
- Request: `GET /cycles?status=current`
- Result: Only cycles with status "current"

### Case Sensitivity
Status filtering is **case-sensitive**. "Current" and "current" are treated as different statuses.

### Whitespace Handling
Leading and trailing whitespace is automatically trimmed from status values.

**Example:**
- Request: `GET /cycles?status= current , unbilled `
- Processed as: `current,unbilled`

## Usage Examples

### Single Status Filter (Backward Compatible)
```bash
curl -X GET "http://localhost:3001/cycles?status=current" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Multiple Status Filter
```bash
curl -X GET "http://localhost:3001/cycles?status=current,unbilled,billed" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Combined with Other Filters
```bash
curl -X GET "http://localhost:3001/cycles?status=current,unbilled&start_date=2024-01-01&end_date=2024-12-31" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### With Search and Pagination
```bash
curl -X GET "http://localhost:3001/cycles?status=current,unbilled&q=2024&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Response Format

The response maintains the standard cycle pagination format:

```json
{
  "data": [
    {
      "id": "cycle-id-1",
      "cycle_no": 1,
      "status": "current",
      "cycle_start_date": "2024-01-01T00:00:00Z",
      "cycle_end_date": "2024-01-31T23:59:59Z",
      "project_usages": [
        // ... project usage data if included
      ]
    },
    {
      "id": "cycle-id-2", 
      "cycle_no": 2,
      "status": "unbilled",
      "cycle_start_date": "2024-02-01T00:00:00Z",
      "cycle_end_date": "2024-02-29T23:59:59Z",
      "project_usages": [
        // ... project usage data if included
      ]
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 2,
    "total_pages": 1
  }
}
```

## Implementation Details

### Database Query
The status filtering is implemented using SQL `IN` clause for optimal performance:

```sql
SELECT * FROM cycles 
WHERE status IN ('current', 'unbilled', 'billed')
```

### Performance Considerations
- The query uses an index on `cycles.status` for optimal performance
- Multiple status filtering is as efficient as single status filtering
- No performance degradation compared to the previous implementation

## Error Handling

### Empty Status Parameter
If the `status` parameter is empty or contains only whitespace, no status filtering is applied:

```bash
# These requests return all cycles (no status filtering)
GET /cycles?status=
GET /cycles?status=   
```

### Non-existent Status Values
If you filter by status values that don't exist, the API will return cycles that match the valid statuses:

```bash
GET /cycles?status=current,nonexistent
# Returns: Only cycles with status "current"
```

### All Invalid Status Values
If all status values are invalid/non-existent, the API returns an empty result set:

```bash
GET /cycles?status=invalid1,invalid2
# Returns: {"data": [], "meta": {"total": 0, ...}}
```

## Common Status Values

Based on the codebase, common cycle status values include:
- `current` - The currently active cycle
- `unbilled` - Cycles that have ended but haven't been billed yet
- `billed` - Cycles that have been billed
- `closed` - Cycles that are closed/archived

## Compatibility

This feature is fully compatible with all existing cycle filtering options:

- ✅ Date range filtering (`start_date=...&end_date=...`)
- ✅ Search functionality (`q=search-term`)
- ✅ Pagination (`page=1&limit=10`)
- ✅ Ordering (`order_by=cycle_start_date DESC`)

## Migration Notes

This is a **non-breaking change**. Existing API calls will continue to work exactly as before. The enhanced status filtering only applies when comma-separated values are provided.

## Testing

See `test_cycle_status_array.http` for comprehensive test cases and examples.
