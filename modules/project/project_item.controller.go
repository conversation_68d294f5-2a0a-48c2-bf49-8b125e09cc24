package project

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectItemController struct {
}

func (m ProjectItemController) Pagination(c core.IHTTPContext) error {
	projectItemSvc := services.NewProjectItemService(c)
	res, ierr := projectItemSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m ProjectItemController) Find(c core.IHTTPContext) error {
	projectItemSvc := services.NewProjectItemService(c)
	projectItem, err := projectItemSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.<PERSON><PERSON><PERSON>())
	}

	return c.JSO<PERSON>(http.StatusOK, projectItem)
}

func (m ProjectItemController) Create(c core.IHTTPContext) error {
	input := &requests.ProjectItemCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectItemSvc := services.NewProjectItemService(c)
	payload := &services.ProjectItemCreatePayload{}
	_ = utils.Copy(payload, input)
	projectItem, err := projectItemSvc.Create(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, projectItem)
}

func (m ProjectItemController) Update(c core.IHTTPContext) error {
	input := &requests.ProjectItemUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectItemSvc := services.NewProjectItemService(c)
	payload := &services.ProjectItemUpdatePayload{}
	_ = utils.Copy(payload, input)
	projectItem, err := projectItemSvc.Update(c.Param("item_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, projectItem)
}

func (m ProjectItemController) Delete(c core.IHTTPContext) error {
	projectItemSvc := services.NewProjectItemService(c)
	err := projectItemSvc.Delete(c.Param("item_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
