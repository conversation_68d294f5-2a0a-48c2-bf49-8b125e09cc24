# Project Usages API Documentation

## Overview
The Project Usages API provides endpoints for retrieving usage data for specific projects. By default, it returns usage data for the current billing cycle only, but can be configured to return data for all cycles.

## Authentication
All endpoints require authentication using <PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <your_access_token>
```

## Endpoints

### 1. Get Project Usages
**GET** `/projects/{id}/usages`

Retrieves usage data for a specific project with pagination support.

#### Path Parameters
- `id`: The UUID of the project

#### Query Parameters
- `all` (optional): Set to "true" to retrieve usage data from all cycles. Default: false (current cycle only)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search query (searches in ID and amount fields)
- `order_by` (optional): Order by field (default: "created_at DESC")

#### Example Requests

##### Get Current Cycle Usages (Default)
```bash
GET /projects/550e8400-e29b-41d4-a716-************/usages?page=1&limit=10
```

##### Get All Cycles Usages
```bash
GET /projects/550e8400-e29b-41d4-a716-************/usages?all=true&page=1&limit=10
```

##### Get Usages with Search
```bash
GET /projects/550e8400-e29b-41d4-a716-************/usages?q=1000&page=1&limit=10
```

#### Example Response
```json
{
  "data": [
    {
      "id": "usage-uuid-1",
      "project_id": "550e8400-e29b-41d4-a716-************",
      "cycle_id": "cycle-uuid-here",
      "amount": 1250.75,
      "official_amount": 1200.00,
      "hour_count": 168,
      "timestamp": "2024-01-15T10:30:00Z",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "project": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Digital Transformation Project",
        "code": "DTP-001",
        "account_name": "project-account",
        "account_id": "account-id-123"
      },
      "cycle": {
        "id": "cycle-uuid-here",
        "cycle_start_date": "2024-01-01T00:00:00Z",
        "cycle_end_date": "2024-01-31T23:59:59Z",
        "status": "current",
        "hour_count": 744,
        "total_cost": 15000.50,
        "total_official_cost": 14500.00
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_pages": 1
  }
}
```

### 2. Get Project Usages by Cycle
**GET** `/projects/{id}/usages/cycles/{cycle_id}`

Retrieves usage data for a specific project filtered by a specific billing cycle.

#### Path Parameters
- `id`: The UUID of the project
- `cycle_id`: The UUID of the billing cycle

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search query (searches in ID and amount fields)
- `order_by` (optional): Order by field (default: "timestamp DESC")

#### Example Requests

##### Get Usages for Specific Cycle
```bash
GET /projects/550e8400-e29b-41d4-a716-************/usages/cycles/cycle-uuid-here?page=1&limit=10
```

##### Get Usages for Specific Cycle with Search
```bash
GET /projects/550e8400-e29b-41d4-a716-************/usages/cycles/cycle-uuid-here?q=1000&page=1&limit=5
```

#### Example Response
```json
{
  "data": [
    {
      "id": "usage-uuid-1",
      "project_id": "550e8400-e29b-41d4-a716-************",
      "cycle_id": "cycle-uuid-here",
      "amount": 1250.75,
      "official_amount": 1200.00,
      "hour_count": 168,
      "timestamp": "2024-01-15T10:30:00Z",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "project": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Digital Transformation Project",
        "code": "DTP-001",
        "account_name": "project-account",
        "account_id": "account-id-123"
      },
      "cycle": {
        "id": "cycle-uuid-here",
        "cycle_start_date": "2024-01-01T00:00:00Z",
        "cycle_end_date": "2024-01-31T23:59:59Z",
        "status": "completed",
        "hour_count": 744,
        "total_cost": 15000.50,
        "total_official_cost": 14500.00
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_pages": 1
  }
}
```

## Data Model

### ProjectUsage Object
- `id`: UUID - Unique identifier for the usage record
- `project_id`: UUID - ID of the associated project
- `cycle_id`: UUID - ID of the billing cycle
- `amount`: Float - Usage amount
- `official_amount`: Float - Official usage amount
- `hour_count`: Integer - Number of hours in this usage period
- `timestamp`: DateTime - When the usage was recorded
- `created_at`: DateTime - When the record was created
- `updated_at`: DateTime - When the record was last updated
- `project`: Object - Related project information (included when available)
- `cycle`: Object - Related cycle information (included when available)

## Behavior

### Default Behavior (Current Cycle Only)
By default, the `/projects/{id}/usages` endpoint returns usage data for the current billing cycle only. This is determined by cycles with `status = "current"`.

### All Cycles Mode
When `all=true` is specified in the `/projects/{id}/usages` endpoint, it returns usage data from all billing cycles for the project, ordered by creation date (newest first).

### Specific Cycle Mode
The `/projects/{id}/usages/cycles/{cycle_id}` endpoint returns usage data for a specific billing cycle only. This allows precise filtering by cycle and is useful for historical data analysis or reporting for specific billing periods.

### Search Functionality
The search parameter (`q`) allows filtering usage records by:
- Usage record ID (partial match, case-insensitive)
- Amount value (partial match)

### Pagination
All responses are paginated with the following structure:
- `data`: Array of usage records
- `pagination`: Object containing page info (page, limit, total, total_pages)

## Error Responses

### Project Not Found (404)
```json
{
  "code": "NOT_FOUND",
  "message": "Project not found"
}
```

### Cycle Not Found (404)
```json
{
  "code": "NOT_FOUND",
  "message": "Cycle not found"
}
```

### Unauthorized (401)
```json
{
  "code": "UNAUTHORIZED",
  "message": "Authentication required"
}
```

### Invalid Parameters (400)
```json
{
  "code": "INVALID_PARAMETERS",
  "message": "Invalid query parameters"
}
```

## Usage Examples

### Basic Usage (Current Cycle)
```bash
curl -X GET "http://localhost:3001/projects/your-project-id/usages" \
  -H "Authorization: Bearer your-token"
```

### All Cycles with Pagination
```bash
curl -X GET "http://localhost:3001/projects/your-project-id/usages?all=true&page=1&limit=5" \
  -H "Authorization: Bearer your-token"
```

### Search in Current Cycle
```bash
curl -X GET "http://localhost:3001/projects/your-project-id/usages?q=1000" \
  -H "Authorization: Bearer your-token"
```

### Get Usages for Specific Cycle
```bash
curl -X GET "http://localhost:3001/projects/your-project-id/usages/cycles/your-cycle-id" \
  -H "Authorization: Bearer your-token"
```

### Get Usages for Specific Cycle with Pagination and Search
```bash
curl -X GET "http://localhost:3001/projects/your-project-id/usages/cycles/your-cycle-id?page=1&limit=10&q=search-term" \
  -H "Authorization: Bearer your-token"
```

## Endpoint Comparison

| Endpoint | Purpose | Cycle Filter |
|----------|---------|--------------|
| `/projects/{id}/usages` | Get project usages (default: current cycle) | Current cycle by default, all cycles with `all=true` |
| `/projects/{id}/usages/cycles/{cycle_id}` | Get project usages for specific cycle | Specific cycle only |

The new endpoint provides more precise control over cycle filtering and is particularly useful for:
- Historical data analysis
- Specific billing period reports
- Comparing usage across different cycles
- Integration with cycle management workflows
